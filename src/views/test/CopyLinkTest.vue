<template>
  <ZPage title="弹窗组件测试" backgroundColor="#f5f5f5">
    <div class="test-container">
      <h2>弹窗组件测试页面</h2>

      <div class="test-section">
        <h3>基础功能测试</h3>
        <div class="button-group">
          <button @click="showBasicDialog" class="test-btn primary">显示基础弹窗</button>
          <button @click="showCustomDialog" class="test-btn secondary">显示自定义弹窗</button>
          <button @click="showLongUrlDialog" class="test-btn tertiary">显示长链接弹窗</button>
        </div>
      </div>

      <div class="test-section">
        <h3>当前状态</h3>
        <div class="status-info">
          <p><strong>弹窗状态:</strong> {{ showDialog ? "显示中" : "已隐藏" }}</p>
          <p><strong>当前URL:</strong> {{ currentUrl || "无" }}</p>
          <p><strong>复制次数:</strong> {{ copyCount }}</p>
          <p><strong>最后复制的URL:</strong> {{ lastCopiedUrl || "无" }}</p>
        </div>
      </div>

      <div class="test-section">
        <h3>事件日志</h3>
        <div class="event-log">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-data">{{ log.data }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="no-logs">暂无事件日志</div>
        </div>
      </div>

      <!-- 强制更新弹窗测试区域 -->
      <div class="test-section">
        <h3>ForceUpdateDialog 强制更新弹窗测试</h3>
        <div class="button-group">
          <button @click="showBasicUpdateDialog" class="test-btn primary">显示基础更新弹窗</button>
          <button @click="showCustomUpdateDialog" class="test-btn secondary">
            显示自定义更新弹窗
          </button>
          <button @click="showClosableUpdateDialog" class="test-btn tertiary">
            显示可关闭更新弹窗
          </button>
        </div>
      </div>

      <div class="test-section">
        <h3>更新弹窗状态</h3>
        <div class="status-info">
          <p><strong>更新弹窗状态:</strong> {{ updateDialogVisible ? "显示中" : "已隐藏" }}</p>
          <p><strong>更新点击次数:</strong> {{ updateClickCount }}</p>
          <p><strong>最后更新时间:</strong> {{ lastUpdateTime || "无" }}</p>
        </div>
      </div>
    </div>

    <!-- CopyLinkTip 组件 -->
    <CopyLinkTip
      v-model="showDialog"
      :copy-url="currentUrl"
      :title="currentTitle"
      :description="currentDescription"
      @close="handleClose"
      @copy-success="handleCopySuccess"
    />

    <!-- 基础更新弹窗 -->
    <ForceUpdateDialog
      v-model="basicUpdateDialogVisible"
      @update="handleUpdate"
      @close="handleUpdateClose"
    />

    <!-- 自定义更新弹窗 -->
    <ForceUpdateDialog
      v-model="customUpdateDialogVisible"
      :title="customUpdateConfig.title"
      :version="customUpdateConfig.version"
      :description-title="customUpdateConfig.descriptionTitle"
      :update-list="customUpdateConfig.updateList"
      :button-text="customUpdateConfig.buttonText"
      @update="handleCustomUpdate"
      @close="handleUpdateClose"
    />

    <!-- 可关闭更新弹窗 -->
    <ForceUpdateDialog
      v-model="closableUpdateDialogVisible"
      :has-close-btn="true"
      :mask-closable="true"
      title="Optional Update Available"
      version="V1.6.0"
      :update-list="['Performance improvements', 'Bug fixes', 'New features']"
      button-text="Update Now"
      @update="handleUpdate"
      @close="handleUpdateClose"
    />
  </ZPage>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
// @ts-ignore
import CopyLinkTip from "@/components/ZPopDialog/CopyLinkTip.vue";
// @ts-ignore
import ForceUpdateDialog from "@/components/ZPopDialog/ForceUpdateDialog.vue";

// CopyLinkTip 响应式数据
const showDialog = ref(false);
const currentUrl = ref("");
const currentTitle = ref("");
const currentDescription = ref("");
const copyCount = ref(0);
const lastCopiedUrl = ref("");
const eventLogs = ref<Array<{ time: string; event: string; data: string }>>([]);

// ForceUpdateDialog 响应式数据
const basicUpdateDialogVisible = ref(false);
const customUpdateDialogVisible = ref(false);
const closableUpdateDialogVisible = ref(false);
const updateClickCount = ref(0);
const lastUpdateTime = ref("");

// 计算属性：任一更新弹窗是否显示
const updateDialogVisible = computed(
  () =>
    basicUpdateDialogVisible.value ||
    customUpdateDialogVisible.value ||
    closableUpdateDialogVisible.value
);

// 自定义更新配置
const customUpdateConfig = ref({
  title: "发现新版本！",
  version: "V2.0.0",
  descriptionTitle: "更新内容：",
  updateList: [
    "修复了已知的安全漏洞",
    "优化了应用性能和稳定性",
    "新增了用户反馈功能",
    "改进了界面设计和用户体验",
    "支持更多语言和地区",
  ],
  buttonText: "立即更新",
});

// 添加事件日志
const addLog = (event: string, data: string = "") => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  eventLogs.value.unshift({ time, event, data });
  // 只保留最近20条日志
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20);
  }
};

// 基础弹窗
const showBasicDialog = () => {
  currentUrl.value = "https://example.com/download";
  currentTitle.value = "App Exclusive Rewards!";
  currentDescription.value = "Download now and unlock your special bonus.";
  showDialog.value = true;
  addLog("显示基础弹窗", currentUrl.value);
};

// 自定义弹窗
const showCustomDialog = () => {
  currentUrl.value = "https://myapp.com/special-offer";
  currentTitle.value = "Special Promotion!";
  currentDescription.value = "Get 50% off on your first purchase. Limited time offer!";
  showDialog.value = true;
  addLog("显示自定义弹窗", currentUrl.value);
};

// 长链接弹窗
const showLongUrlDialog = () => {
  currentUrl.value =
    "https://very-long-domain-name.example.com/path/to/very/long/url/with/many/parameters?param1=value1&param2=value2&param3=value3&param4=value4&param5=value5";
  currentTitle.value = "Download Our App";
  currentDescription.value =
    "Access exclusive features and get the best experience with our mobile application.";
  showDialog.value = true;
  addLog("显示长链接弹窗", `${currentUrl.value.substring(0, 50)}...`);
};

// 事件处理
const handleClose = () => {
  addLog("弹窗关闭");
};

const handleCopySuccess = (url: string) => {
  copyCount.value++;
  lastCopiedUrl.value = url;
  addLog("复制成功", url);
};

// ForceUpdateDialog 事件处理函数
const showBasicUpdateDialog = () => {
  basicUpdateDialogVisible.value = true;
  addLog("显示基础更新弹窗");
};

const showCustomUpdateDialog = () => {
  customUpdateDialogVisible.value = true;
  addLog("显示自定义更新弹窗");
};

const showClosableUpdateDialog = () => {
  closableUpdateDialogVisible.value = true;
  addLog("显示可关闭更新弹窗");
};

const handleUpdate = () => {
  updateClickCount.value++;
  lastUpdateTime.value = new Date().toLocaleString();
  addLog("用户点击更新按钮", "基础更新");
  console.log("用户点击了更新按钮");
  // 这里可以执行实际的更新逻辑
  alert("开始更新...");
};

const handleCustomUpdate = () => {
  updateClickCount.value++;
  lastUpdateTime.value = new Date().toLocaleString();
  addLog("用户点击自定义更新按钮", "自定义更新");
  console.log("用户点击了自定义更新按钮");
  alert("开始自定义更新...");
};

const handleUpdateClose = () => {
  basicUpdateDialogVisible.value = false;
  customUpdateDialogVisible.value = false;
  closableUpdateDialogVisible.value = false;
  addLog("更新弹窗关闭");
};
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
  }

  .test-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 8px;
    }
  }

  .button-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;

    .test-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s ease;

      &.primary {
        background: linear-gradient(135deg, #ffb800 0%, #ff8a00 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 184, 0, 0.3);
        }
      }

      &.secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }

      &.tertiary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .status-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;

    p {
      margin: 8px 0;
      font-size: 14px;

      strong {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .event-log {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;

    .log-item {
      display: flex;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 13px;

      &:last-child {
        border-bottom: none;
      }

      .log-time {
        color: #6c757d;
        font-family: monospace;
        min-width: 80px;
      }

      .log-event {
        color: #495057;
        font-weight: 600;
        min-width: 100px;
      }

      .log-data {
        color: #6c757d;
        word-break: break-all;
        flex: 1;
      }
    }

    .no-logs {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      padding: 20px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .test-container {
    padding: 16px;

    .button-group {
      flex-direction: column;

      .test-btn {
        width: 100%;
      }
    }

    .event-log .log-item {
      flex-direction: column;
      gap: 4px;

      .log-time,
      .log-event {
        min-width: auto;
      }
    }
  }
}
</style>
