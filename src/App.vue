<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";

import TabBar from "@/components/tabbar/Index.vue";
import DepositDialog from "@/components/DepositDialog/index.vue";
import WithDrawDialog from "@/components/WithDrawDialog/index.vue";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";

// utils
import { getRouteKey, keepAliveNames } from "@/utils/RouteCache";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { GeetestMgr } from "@/utils/GeetestMgr";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";

// store
import { useKycStore } from "@/stores/kyc";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { appInitializer } from "@/utils/preload/AppInitializer";
import { loginManager } from "@/utils/managers/LoginManager";
import { useMayaToken } from "@/composables/useMayaToken";

// 启动页
import SplashScreen from "@/components/SplashScreen.vue";

// 浮动
import TurntableButton from "@/components/Single-purpose/TurntableButton.vue";
import EnvelopeButton from "@/components/Single-purpose/EnvelopeButton.vue";

// 弹窗提示组件
import EnvelopeTip from "@/components/ZPopDialog/EnvelopeTip.vue";
import TurntablePop from "@/components/ZPopDialog/TurntablePop.vue";

const autoPopMgrStore = useAutoPopMgrStore();
const kycStore = useKycStore();
const globalStore = useGlobalStore();
const gameStore = useGameStore();
const route = useRoute();
const showTabBar = computed(() => route.meta.showTabBar);

const { startRefresh } = useMayaToken();

// 应用初始化进度
const initProgress = appInitializer.getProgress();

loginManager.setChannel();

const isLoading = ref(true);

// 红包小图
const showFloatEnvelopeButton = computed(() => {
  return ["Home"].includes(route.name as string) && autoPopMgrStore.isNeedShowEnvelopePop();
});
// 转盘小图
const showTurntableButton = computed(() => {
  const showButtonByName = [
    "Home",
    "Promos",
    "Message",
    "News",
    "GameCategories",
    "CasinoCate",
  ].includes(route.name as string);
  if (globalStore.token) {
    return showButtonByName && autoPopMgrStore.spinInfo.is_start === 1;
  }
  return showButtonByName;
});
const keepAliveName = keepAliveNames;
const getKey = () => getRouteKey(route);

// 公用前置接口初始化
const initializeApp = async () => {
  try {
    await appInitializer.initialize((progress) => {
      // 更新进度显示
      initProgress.value = progress.value;
    });
    return true;
  } catch (error) {
    console.error("应用初始化失败:", error);
    initProgress.value = 100; // 即使失败也要完成进度
    return false;
  }
};

// 应用挂载时开始初始化
onMounted(async () => {
  autoPopMgrStore.hasPop = false;
  KycMgr.instance.clearData();
  // KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
  // 先执行登录逻辑
  await loginManager.preLogin();

  // maya token 刷新
  if (globalStore.token) {
    startRefresh();
  }
  try {
    // 获取基础配置接口
    gameStore.getGameConfigData();
    // 获取维护列表
    gameStore.getMaintenanceList(true);
    // 在渠道确定后初始化 Geetest
    await GeetestMgr.instance.initializeAfterChannelSet();

    // 基础接口
  } catch (error) {
    console.error("Geetest 初始化失败:", error);
  }

  // 然后执行应用初始化
  await initializeApp();
});

const handleSplashLoaded = () => {
  isLoading.value = false;
};

// 获取环境信息
const envMode = import.meta.env.MODE;

// 获取打包时间（只在构建时才有值）
const buildTime = computed(() => {
  // 开发环境下 __BUILD_TIME__ 可能未定义，只在构建环境显示
  try {
    return typeof __BUILD_TIME__ !== "undefined" ? __BUILD_TIME__ : "";
  } catch {
    return "";
  }
});

// 是否显示调试信息（非生产环境才显示）
const showDebugInfo = computed(() => {
  return envMode !== "production";
});
</script>
<template>
  <div class="h5-app-container">
    <SplashScreen v-if="isLoading" :external-progress="initProgress" @loaded="handleSplashLoaded" />
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <keep-alive :include="keepAliveName">
          <component :is="Component" :key="getKey()" />
        </keep-alive>
      </transition>
    </router-view>
    <TabBar v-if="showTabBar" />
    <!-- 充值 -->
    <DepositDialog />
    <!-- 提现 -->
    <WithDrawDialog />
    <!-- kyc 需要用到的设置手机号弹窗 -->
    <VerifyDialogChangePhone
      v-model:showNextDialog="kycStore.showPhoneChangeDialog"
      :verifyType="PN_VERIFY_TYPE.SetPhoneNumber"
      :succCallBack="() => (autoPopMgrStore.showKycTip = false)"
    />
    <TurntablePop />
    <EnvelopeTip></EnvelopeTip>

    <!-- 浮动按钮容器 - 确保在 #app 容器内 -->
    <div class="floating-buttons-container">
      <!-- 首冲按钮 -->
      <ZFloatingBubble v-if="showFloatEnvelopeButton" :right="40" :bottom="280">
        <EnvelopeButton />
      </ZFloatingBubble>
      <!-- 大转盘按钮 -->
      <ZFloatingBubble v-if="showTurntableButton" :right="60" :bottom="200">
        <TurntableButton />
      </ZFloatingBubble>
      <!-- 调试信息 - 仅非生产环境显示 -->
      <ZFloatingBubble v-if="showDebugInfo" :right="40" :top="100">
        <div class="develop">
          <div>渠道：{{ globalStore.channel }}</div>
          <div>环境：{{ envMode }}</div>
          <div>版本：{{ ALL_APP_SOURCE_CONFIG.app_version }}</div>
          <div v-if="buildTime">{{ buildTime }}</div>
        </div>
      </ZFloatingBubble>
    </div>
  </div>
</template>

<style scoped lang="scss">
.h5-app-container {
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  min-height: 100vh;
  background: #f5f6fa;
}

.floating-buttons-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.develop {
  background-color: rgba($color: #000000, $alpha: 0.5);
  color: #fff;
  padding: 5px 10px;
  border-radius: 10px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  font-family: D-DIN;
}
</style>
