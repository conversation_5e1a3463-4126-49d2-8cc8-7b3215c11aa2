import { defineStore } from "pinia";
import {
  getDepositRule,
  paymentBalanceAdd,
  paymentGcash,
  gcashSetJumpType,
  rechargeWithdraw,
} from "@/api/deposit";
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { getBrowserName, isBrowser } from "@/utils/core/tools";
import {
  E_CHANEL_TYPE,
  METHODS,
  PAY_METHOD,
  MAINTENANCETIPCODE,
} from "@/utils/config/GlobalConstant";
import router from "@/router/index";
import { getEnvConfig } from "@/utils/config/Config";
import { getGlobalDialog } from "@/enter/vant";
import { showToast } from "vant";
import { getLocalStorage, removeLocalStorage, setLocalStorage } from "@/utils/core/Storage";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

const SELECT_PAYMENT = "SELECT_PAYMENT";

// 充值项类型声明
interface RechargeItem {
  id: number;
  name: string;
  account_type: string;
  min: string;
  max: string;
  first_restriction?: string | number;
  sort?: number;
  amount?: number;
  award?: number;
}

export const useDepositStore = defineStore("deposit", {
  state: () => ({
    // 弹窗显示
    show: false,
    // 当前充值方式
    curRechangeMethods: "",
    // 输入（选中）的充值金额
    selectedAmount: "" as string | number,
    // 输入校验提示
    vaildTip: "",
    // 充值方式列表
    rechargeList: [] as RechargeItem[],
    // 首次充值限制
    firstRestriction: 0,
    // 是否首充
    isFirstDeposit: false,
    // 首充奖励列表
    firstDepositAwradList: [] as RechargeItem[],
    // 奖励金额
    awardNum: 0,
    // 总金额
    totalAmount: 0,
    // 充值项ID
    rechargeID: 0,
    // 支付方式对应的值
    payMethodValue: null as string | null,
    // 当前充值方式的最小充值金额
    minAmount: 0,
    // 当前充值方式的最大充值金额
    maxAmount: 0,
    // 定时器
    depositTimeout: null as ReturnType<typeof setTimeout> | null,
    // 是否连续充值
    batchCheck: false,
    maximumSingleRecharge: 0, // 判断自动选中连续充值的最大值
    // 预打开的窗口（用于移动端兼容性）
    preOpenedWindow: null as Window | null,
  }),
  getters: {
    // 渠道来源
    CHANEL_TYPE(): E_CHANEL_TYPE {
      const globalStore = useGlobalStore();
      return globalStore.channel;
    },
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel(): boolean {
      const globalStore = useGlobalStore();
      const channel = globalStore.channel?.toLowerCase();
      return ["gcash", "maya"].includes(channel);
    },
    $dialog() {
      return getGlobalDialog();
    },
  },
  actions: {
    // 打开充值入口
    async openDialog(depositNum?: number) {
      const globalStore = useGlobalStore();
      if (!globalStore.token) {
        router.push("/login");
        return;
      }
      if (depositNum) {
        this.selectedAmount = depositNum;
      } else {
        this.selectedAmount = "";
      }
      if (this.CHANEL_TYPE === E_CHANEL_TYPE.G_CASH) {
        this.backMiniBuyTips();
        return;
      }
      showZLoading();
      try {
        // 初始化数据
        await this.init();

        // 打开弹窗
        this.show = true;
      } catch (error) {
      } finally {
        closeZLoading();
      }
    },
    // 请求入口
    async init() {
      try {
        if (E_CHANEL_TYPE.WEB === this.CHANEL_TYPE) {
          const amountConfigRes = await this.loadAmountConfig();
          this.initPayMethod(amountConfigRes);
        } else if ([E_CHANEL_TYPE.MAYA, E_CHANEL_TYPE.G_CASH].includes(this.CHANEL_TYPE)) {
          await this.loadFirstDeposit();
          const amountConfigRes = await this.loadAmountConfig();
          this.curRechangeMethods = amountConfigRes?.[0]?.name || "";
          this.payMethodValue = amountConfigRes?.[0]?.account_type || "";
          this.setLabAmount(0);
        }

        if (!this.maximumSingleRecharge) {
          const res = getLocalStorage("maximum_single_recharge") || 0;
          if (res) {
            this.maximumSingleRecharge = res ? Number(res) : 0;
            removeLocalStorage("maximum_single_recharge");
          }
        }
      } catch (error) {
        console.error("Deposit init failed:", error);
        showToast("The current network is abnormal, please try again later.");
        throw error; // 重新抛出错误，让 openDialog 处理
      }
    },
    // 初始化支付方式
    initPayMethod(amountConfigRes: RechargeItem[]) {
      let lastPayMethod = getLocalStorage(SELECT_PAYMENT);
      let payMethodIndex = amountConfigRes.findIndex((item) => item.name === lastPayMethod);
      if (payMethodIndex > -1) {
        this.curRechangeMethods = amountConfigRes[payMethodIndex].name;
        this.payMethodValue = amountConfigRes[payMethodIndex].account_type;
        this.setLabAmount(payMethodIndex);
        return;
      }
      this.curRechangeMethods = amountConfigRes?.[0]?.name || "";
      this.payMethodValue = amountConfigRes?.[0]?.account_type || "";
      this.setLabAmount(0);
    },
    // 设置当前充值方式的金额区间
    setLabAmount(index: number) {
      let minAmount = parseFloat(this.rechargeList[index]?.min);
      let maxAmount = parseFloat(this.rechargeList[index]?.max);
      let first_restriction = this.rechargeList[index]?.first_restriction || 0;
      if (this.isFirstDeposit && parseFloat(first_restriction as string) > 0) {
        minAmount = parseFloat(first_restriction as string);
      }
      this.minAmount = minAmount;
      this.maxAmount = maxAmount;
      this.firstRestriction = Number(first_restriction);
    },
    // 获取是否首充、充值项对应奖励金额
    async loadFirstDeposit() {
      try {
        const response = await getDepositRule();
        const res = response.data || response;

        this.isFirstDeposit = res.is_first_charge || false;
        let rechargeList: RechargeItem[] = res.recharge || [];
        rechargeList.sort((a, b) => a.id - b.id);
        this.firstDepositAwradList = rechargeList;

        if (!this.firstDepositAwradList || this.firstDepositAwradList.length === 0) {
          this.isFirstDeposit = false;
        }

        return res;
      } catch (error) {
        console.error("Load first deposit failed:", error);
        this.isFirstDeposit = false;
        this.firstDepositAwradList = [];
        throw new Error("Failed to load first deposit information");
      }
    },
    // 获取充值方式及金额配置
    async loadAmountConfig() {
      try {
        const response = await rechargeWithdraw({
          appChannel: this.CHANEL_TYPE,
        });
        const res = response;

        let recharge: RechargeItem[] = res.recharge || [];
        recharge.sort((a, b) => {
          if (a.sort !== b.sort) {
            return b.sort! - a.sort!;
          } else {
            if (a.name && b.name) {
              return b.name.localeCompare(a.name);
            }
            return 0;
          }
        });
        this.rechargeList = recharge;
        console.log("hahahah", recharge, this.rechargeList);
        return recharge;
      } catch (error) {
        console.error("Load amount config failed:", error);
        this.rechargeList = [];
        throw new Error("Failed to load payment methods");
      }
    },
    // 设置当前充值方式
    setCurReChangeName(name: string) {
      const index = this.rechargeList.findIndex((item) => item.name === name);
      this.curRechangeMethods = name;
      this.payMethodValue = this.rechargeList[index].account_type || "";
      this.setLabAmount(index);
      setLocalStorage(SELECT_PAYMENT, name);
      // 如果paymethod>6个，调整顺序
      if (this.rechargeList.length > 6) {
        const selectPayItem = this.rechargeList[index];
        this.rechargeList.splice(index, 1);
        this.rechargeList.unshift(selectPayItem);
      }
      this.resetData();
    },
    // 点击选中金额
    setSelectedAmount(amount: string) {
      this.selectedAmount = amount;
      this.getVaildAmountErr(amount);
      this.calcAmount(+amount);
    },
    // 计算总额和奖励金额
    calcAmount(value: number) {
      if (this.vaildTip) return;
      const calValue = this.getFirstBonusNum(value);
      this.awardNum = calValue;
      this.totalAmount = Number(value) + Number(calValue);
    },
    // 输入框 input事件
    handleCustomAmountInput(event: Event) {
      const input = event.target as HTMLInputElement;
      let value = input.value.replace(/\D/g, "");
      this.setSelectedAmount(value);
      this.getVaildAmountErr(value);
      this.calcAmount(+value);
    },
    // 校验金额范围提示
    getVaildAmountErr(value: string | number) {
      let min = this.minAmount;
      let max = this.maxAmount;
      let vaildTipStr = "";
      const numValue = Number(value);
      if (value !== "" && numValue < min) {
        vaildTipStr = `The minimum amount is ${min}₱`;
      } else if (value !== "" && numValue > max) {
        vaildTipStr = `The maximum amount is ${max}₱`;
      } else if (value === "") {
        vaildTipStr = `Enter Amount ${min} - ${max}₱`;
      } else {
        vaildTipStr = "";
      }
      this.vaildTip = vaildTipStr;
      return vaildTipStr;
    },
    //获取充值奖励, 获取充值金额对应的id（小于最接近那个充值金额，的充值id）
    getChargeId(chargeNum: number) {
      if (this.firstDepositAwradList.length === 0 || !chargeNum) {
        return 0;
      }
      let totalLen = this.firstDepositAwradList.length;
      if (chargeNum >= (this.firstDepositAwradList[totalLen - 1].amount || 0)) {
        return this.firstDepositAwradList[totalLen - 1].id;
      }
      for (let idx = 0; idx < totalLen; idx++) {
        let element = this.firstDepositAwradList[idx];
        if (chargeNum < (element.amount || 0)) {
          if (idx === 0) {
            return 0;
          } else {
            return this.firstDepositAwradList[idx - 1].id;
          }
        }
      }
      return 0;
    },
    // 获取充值金额对应的award（小于最接近那个充值金额，的充值奖励）
    getFirstBonusNum(chargeNum: number) {
      if (this.firstDepositAwradList.length === 0 || !chargeNum) {
        return 0;
      }
      let totallength = this.firstDepositAwradList.length;
      if (chargeNum >= (this.firstDepositAwradList[totallength - 1].amount || 0)) {
        return this.firstDepositAwradList[totallength - 1].award || 0;
      }
      for (let index = 0; index < totallength; index++) {
        let element = this.firstDepositAwradList[index];
        if (chargeNum < (element.amount || 0)) {
          if (index === 0) {
            return 0;
          } else {
            return this.firstDepositAwradList[index - 1].award || 0;
          }
        }
      }
      return 0;
    },
    //10秒超时处理 关闭响应
    async handleTimeout() {
      this.$dialog({
        title: "Tips",
        message: "Your deposit request is currently being processed. Please kindly wait.",
        confirmText: "Done",
        showCancelButton: false,
        onConfirm: async () => {
          this.show = false;
          router.push("/account/bet-order");
        },
      });
    },
    // 清掉充值超时定时器
    stopDepTimeoutSchedule() {
      if (this.depositTimeout) {
        clearTimeout(this.depositTimeout);
        this.depositTimeout = null;
      }
    },
    // GCash mini-program充值提醒
    backMiniBuyTips() {
      this.$dialog({
        title: "Quick Reminder",
        message: "Please click 'Go Deposit' to return to the GCash mini-program for top-up.",
        confirmText: "Go Deposit",
        showClose: true,
        showCancelButton: false,
        onConfirm: async () => {
          try {
            const response = await gcashSetJumpType({
              type: this.selectedAmount ? parseInt(this.selectedAmount) : 10,
            });
            const { code } = response;

            if (code === 200 || code === 0) {
              // 使用 MobileWindowManager 处理移动端兼容性
              const success = MobileWindowManager.navigateToUrl(
                getEnvConfig().VITE_GCASH_SHOP_URL,
                this.preOpenedWindow
              );
              if (!success) {
                console.error("Failed to open GCash URL");
                showToast("Failed to open GCash, please try again");
              } else {
                // 成功打开后清理窗口引用
                this.preOpenedWindow = null;
              }
              this.show = false;
            } else if (code === MAINTENANCETIPCODE) {
              this.show = false;
              router.push("/system/maintenance");
            } else {
              throw new Error("GCash jump setup failed");
            }
          } catch (error) {
            console.error("GCash jump failed:", error);
            this.$dialog({
              title: "Tips",
              message: "Failed to setup GCash redirect. Please try again.",
              confirmText: "Done",
              showCancelButton: false,
            });
          }
        },
      });
    },
    // 预打开窗口（在用户点击时调用）
    preOpenWindow() {
      if (MobileWindowManager.isMobile()) {
        this.preOpenedWindow = MobileWindowManager.preOpenWindow();
        if (!this.preOpenedWindow) {
          console.warn("Failed to pre-open window, popup may be blocked");
        }
      }
    },

    // 清理预打开的窗口
    cleanupPreOpenedWindow() {
      if (this.preOpenedWindow) {
        MobileWindowManager.closeWindow(this.preOpenedWindow);
        this.preOpenedWindow = null;
      }
    },

    // 提交充值
    async handleSubmit() {
      if (this.isMiniChannel) {
        // G-Cash 充值方式,跳转到Gcash 小程序
        if (this.CHANEL_TYPE === E_CHANEL_TYPE.G_CASH) {
          this.backMiniBuyTips();
          return;
        }
        //判定是否是首充
        if (this.isFirstDeposit) {
          this.rechargeID = this.getChargeId(Number(this.selectedAmount));
          this.awardNum = this.getFirstBonusNum(Number(this.selectedAmount));
        } else {
          this.rechargeID = 0;
          this.awardNum = 0;
        }
      } else if (this.CHANEL_TYPE === E_CHANEL_TYPE.WEB) {
        this.rechargeID = 0;
        this.awardNum = 0;
      }
      const globalStore = useGlobalStore();

      const params: Record<string, any> = {
        app_package_name: globalStore.userInfo.app_package_name,
        app_version: ALL_APP_SOURCE_CONFIG.app_version,
        amount: +this.selectedAmount,
        award: +this.awardNum,
        email: globalStore.payAccount.email,
        phone: globalStore.userInfo.phone,
        name: globalStore.userInfo.nickname,
        config_id: this.rechargeID,
        identifier: METHODS.MAYA_PAY,
        platform: getBrowserName() + "_" + (isBrowser ? 1 : 0),
      };

      let apiFn = paymentBalanceAdd;
      //支付渠道
      if (this.payMethodValue == PAY_METHOD.MAYA_WEB) {
        params["identifier"] = METHODS.MAYA_WEB;
      } else if (this.payMethodValue == PAY_METHOD.GCASH_WEB) {
        apiFn = paymentGcash;
        params["identifier"] = METHODS.GCASH_WEB;
      }
      // 10秒超时
      if (!this.depositTimeout) {
        this.depositTimeout = setTimeout(this.handleTimeout, 10000);
      }

      try {
        const response = await apiFn(params);
        const { code, data, msg } = response;

        this.stopDepTimeoutSchedule();

        if (code === 200 || code === 0) {
          if (this.CHANEL_TYPE == E_CHANEL_TYPE.WEB) {
            if (data?.paymentUrl) {
              // 连续充值的话，不关闭
              if (this.batchCheck) {
              } else {
                this.show = false;
              }
              // 在移动端预打开窗口（如果还没有预打开）
              if (MobileWindowManager.isMobile() && !this.preOpenedWindow) {
                this.preOpenWindow();
              }
              // 使用 MobileWindowManager 处理移动端兼容性
              const success = MobileWindowManager.navigateToUrl(
                data.paymentUrl,
                this.preOpenedWindow
              );
              if (!success) {
                console.error("Failed to open payment URL:", data.paymentUrl);
                showToast("Failed to open payment page, please try again");
              } else {
                // 成功打开后清理窗口引用
                this.preOpenedWindow = null;
              }
            }
          }
          if (this.isMiniChannel) {
            if (this.CHANEL_TYPE == E_CHANEL_TYPE.G_CASH) {
              if (data.paymentUrl) {
                if (this.batchCheck) {
                } else {
                  this.show = false;
                }
                // 使用 MobileWindowManager 处理移动端兼容性
                const success = MobileWindowManager.navigateToUrl(
                  data.paymentUrl,
                  this.preOpenedWindow
                );
                if (!success) {
                  console.error("Failed to open payment URL:", data.paymentUrl);
                  showToast("Failed to open payment page, please try again");
                } else {
                  // 成功打开后清理窗口引用
                  this.preOpenedWindow = null;
                }
              }
            } else {
              let str = "Deposit Successful! Enjoy Your Nustar Online Gaming Experience!";
              if (this.isFirstDeposit) {
                str =
                  "You have successfully claimed your bonus. Best of luck on your journey ahead!";
              }
              this.$dialog({
                title: "Congratulations",
                message: str,
                confirmText: "Bet Now",
                onConfirm: async () => {
                  useGameStore().clickBetNow(() => (this.show = false));
                },
              });
            }
          }
        } else {
          if (code == MAINTENANCETIPCODE) {
            this.show = false;
            router.push("/system/maintenance");
          } else if (code == 1) {
            this.$dialog({
              title: "Oops",
              message: msg,
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {
                this.show = false;
                router.push("/account/bet-order");
              },
            });
          } else if (code == 103035 || code == 103037) {
            let errstr: string = "";
            if (code == 103035) {
              errstr = "Transaction limit exceeded.";
            }
            if (code == 103037) {
              let payType: string = "";
              if (
                params["identifier"] == METHODS.MAYA_PAY ||
                params["identifier"] == METHODS.MAYA_WEB
              ) {
                payType = "Maya";
              } else if (params["identifier"] == METHODS.GCASH_WEB) {
                payType = "GCash";
              }
              errstr = `Your ${payType} account has insufficient balance.`;
            }
            this.$dialog({
              title: "Oops",
              message: `${errstr}`,
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          } else if (code == 103099) {
            this.$dialog({
              title: "Tips",
              message: "The payment was unsuccessful due to an abnormality.Please try again later.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          } else {
            this.$dialog({
              title: "Tips",
              message: "The payment was unsuccessful due to an abnormality.Please try again later.",
              confirmText: "Done",
              showCancelButton: false,
              onConfirm: async () => {},
            });
          }
        }
      } catch (error) {
        this.stopDepTimeoutSchedule();
        // 出错时清理预打开的窗口
        this.cleanupPreOpenedWindow();
        console.error("Payment submission failed:", error);
        this.$dialog({
          title: "Tips",
          message: "Network error occurred. Please check your connection and try again.",
          confirmText: "Done",
          showCancelButton: false,
          onConfirm: () => {
            this.show = false;
          },
        });
      }
    },
    // 重置输入
    resetData() {
      this.selectedAmount = "";
      this.vaildTip = "";
    },
  },
});
